<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>de.geolykt</groupId>
    <artifactId>enchantments-plus</artifactId>
    <version>4.1.0</version>
    <packaging>jar</packaging>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <repositories>
        <repository>
            <id>spigot-repo</id> <!-- You know what this does, I hope -->
            <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
        </repository>
        <repository>
            <id>geolykt-maven</id> <!-- Artifacts that don't have a repository otherwise -->
            <url>https://geolykt.de/maven/</url>
        </repository>
        <repository>
            <id>jitpack.io</id> <!-- For a few plugins -->
            <url>https://jitpack.io</url>
        </repository>
        <repository> <!-- For WG -->
            <id>sk89q-repo</id>
            <url>https://maven.enginehub.org/repo/</url>
        </repository>
        <!-- For RoseStacker -->
        <repository>
            <id>rosewood-repo</id>
            <url>https://repo.rosewooddev.io/repository/public/</url>
        </repository>
        <repository>
            <id>md_5-repo</id> <!-- For LogBlock -->
            <url>https://repo.md-5.net/content/repositories/public/</url>
        </repository>
        <repository> <!-- And yet another repository for CP, could you guys stop publishing your stuff in 2000 maven repos? -->
            <id>coreprotect-repo</id> <!-- For CP -->
            <url>https://maven.playpro.com</url>
        </repository>
        <repository> <!-- For towny -->
            <id>glaremasters repo</id>
            <url>https://repo.glaremasters.me/repository/towny/</url>
        </repository>
    </repositories>
    <dependencies>
        <dependency>
            <groupId>org.spigotmc</groupId>
            <artifactId>spigot-api</artifactId>
            <version>1.21.4-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.jetbrains/annotations -->
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>23.0.0</version>
            <scope>provided</scope>
        </dependency>

        <!-- BStats, our metrics system -->
        <!-- https://mvnrepository.com/artifact/org.bstats/bstats-bukkit -->
        <dependency>
          <groupId>org.bstats</groupId>
          <artifactId>bstats-bukkit</artifactId>
          <version>3.0.0</version>
          <scope>compile</scope>
       </dependency>

        <!-- World protection plugins to integrate with -->
        <dependency>
            <groupId>com.palmergames.bukkit.towny</groupId>
            <artifactId>towny</artifactId>
            <version>0.98.1.0</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <!-- We don't want to harm our dependency tree with transitive dependencies-->
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Welcome to insanity where we have to use 4 dependencies for 1 class.
             Now, I could've just allowed for transitive depends but that significantly extends build time
             as well as potentially polluting what we have available. (so I do not accidentally use a library used
             by WorldGuard for example) -->
        <dependency>
            <groupId>com.sk89q.worldguard</groupId>
            <artifactId>worldguard-bukkit</artifactId>
            <version>7.0.6-SNAPSHOT</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sk89q.worldguard</groupId>
            <artifactId>worldguard-core</artifactId>
            <version>7.0.6-SNAPSHOT</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sk89q.worldedit</groupId>
            <artifactId>worldedit-bukkit</artifactId>
            <version>7.3.0-SNAPSHOT</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sk89q.worldedit</groupId>
            <artifactId>worldedit-core</artifactId>
            <version>7.3.0-SNAPSHOT</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.TechFortress</groupId>
            <artifactId>GriefPrevention</artifactId>
            <version>16.17.1</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cjburkey</groupId>
            <artifactId>claimchunk</artifactId>
            <version>0.0.23-RC5</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- This dependency is located in the geolykt-maven repo. -->
        <dependency>
            <groupId>io.github.bycubed7</groupId>
            <artifactId>claimedcubes</artifactId>
            <version>1.2.9</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Dependency of ClaimedCubes that we needed to add again in order to link properly -->
        <dependency>
            <groupId>io.github.bycubed7</groupId>
            <artifactId>corecubes</artifactId>
            <version>3.1.2-SNAPSHOT</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Slimefun, for the slimefun compatibility integration -->
        <dependency>
            <groupId>com.github.Slimefun</groupId>
            <artifactId>Slimefun4</artifactId>
            <version>RC-32</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Mobstacker plugins to integrate with -->
        <dependency>
            <groupId>uk.antiperson.stackmob</groupId>
            <artifactId>StackMob</artifactId>
            <version>5.8.4</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>dev.rosewood</groupId>
            <artifactId>rosestacker</artifactId>
            <version>1.5.1</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Logging plugins -->
        <dependency>
            <groupId>de.diddiz</groupId>
            <artifactId>logblock</artifactId>
            <version>********-SNAPSHOT</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.coreprotect</groupId>
            <artifactId>coreprotect</artifactId>
            <version>20.4</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Time to go nuclear -->
        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>9.5</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm-tree</artifactId>
            <version>9.5</version>
            <scope>compile</scope>
        </dependency>

        <!-- TODO Anti-cheat APIs for Anti-cheat integrations-->
    </dependencies>

    <build>
        <defaultGoal>clean package</defaultGoal>
        <resources>
            <resource>
                <directory>resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>.</directory>
                <includes>
                    <include>LICENSE.md</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <target>21</target> <!-- Java 16 is not the best version, but since we are still serving older versions it ought to be enough -->
                    <source>21</source>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.6.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <relocations>
                        <relocation>
                            <pattern>org.bstats</pattern>
                            <shadedPattern>de.geolykt.enchantments_plus.bstats</shadedPattern>
                        </relocation>
                        <relocation>
                            <pattern>org.objectweb.asm</pattern>
                            <shadedPattern>de.geolykt.enchantments_plus.compatibility.hackloader.asm</shadedPattern>
                        </relocation>
                    </relocations>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

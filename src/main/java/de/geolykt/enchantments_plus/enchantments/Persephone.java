/*
 * This file is part of EnchantmentsPlus, a bukkit plugin.
 * Copyright (c) 2015 - 2020 Zedly and Zenchantments contributors.
 * Copyright (c) 2020 - 2021 Geolykt and EnchantmentsPlus contributors
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by 
 * the Free Software Foundation, version 3.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package de.geolykt.enchantments_plus.enchantments;

import java.util.concurrent.ThreadLocalRandom;

import org.bukkit.Location;
import org.bukkit.Material;
import static org.bukkit.Material.SOUL_SAND;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import static org.bukkit.event.block.Action.RIGHT_CLICK_BLOCK;
import org.bukkit.event.player.PlayerInteractEvent;

import de.geolykt.enchantments_plus.CustomEnchantment;
import de.geolykt.enchantments_plus.Storage;
import de.geolykt.enchantments_plus.compatibility.CompatibilityAdapter;
import de.geolykt.enchantments_plus.enums.BaseEnchantments;
import de.geolykt.enchantments_plus.enums.Hand;
import de.geolykt.enchantments_plus.util.AreaOfEffectable;
import de.geolykt.enchantments_plus.util.Tool;
import de.geolykt.enchantments_plus.util.Utilities;

public class Persephone extends CustomEnchantment implements AreaOfEffectable {

    public static final int ID = 41;

    @Override
    public Builder<Persephone> defaults() {
        return new Builder<>(Persephone::new, ID)
                .all("Plants seeds from the player's inventory around them",
                        new Tool[]{Tool.HOE},
                        "Persephone",
                        3, // MAX LVL
                        Hand.RIGHT);
    }

    public Persephone() {
        super(BaseEnchantments.PERSEPHONE);
    }

    @Override
    public boolean onBlockInteract(PlayerInteractEvent evt, int level, boolean usedHand) {
        if (evt.getAction() == RIGHT_CLICK_BLOCK) {
            Player player = evt.getPlayer();
            Location loc = evt.getClickedBlock().getLocation();
            int radiusXZ = (int) getAOESize(level);

            if (Storage.COMPATIBILITY_ADAPTER.persephoneCrops().contains(evt.getClickedBlock().getType())) {
                Block block = loc.getBlock();
                for (int x = -radiusXZ; x <= radiusXZ; x++) {
                    for (int y = -2; y <= 0; y++) {
                        for (int z = -radiusXZ; z <= radiusXZ; z++) {

                            if (block.getRelative(x, y, z).getLocation().distanceSquared(loc)
                                    < radiusXZ * radiusXZ) {
                                if (block.getRelative(x, y, z).getType() == Material.FARMLAND
                                        && Storage.COMPATIBILITY_ADAPTER.airs().contains(block.getRelative(x, y + 1, z).getType())) {
                                    if (evt.getPlayer().getInventory().contains(Material.CARROT)) {
                                        block.getRelative(x, y + 1, z).setType(Material.CARROTS);
                                        Utilities.removeItem(player, Material.CARROT, 1);
                                    } else if (evt.getPlayer().getInventory().contains(Material.POTATO)) {
                                        if (ADAPTER.placeBlock(block.getRelative(x, y + 1, z), player, Material.POTATOES,
                                                null)) {
                                            Utilities.removeItem(player, Material.POTATO, 1);
                                        }
                                    } else if (evt.getPlayer().getInventory().contains(Material.WHEAT_SEEDS)) {
                                        if (ADAPTER.placeBlock(block.getRelative(x, y + 1, z), player, Material.WHEAT, null)) {
                                            Utilities.removeItem(player, Material.WHEAT_SEEDS, 1);
                                        }
                                    } else if (evt.getPlayer().getInventory().contains(Material.BEETROOT_SEEDS)) {
                                        if (ADAPTER.placeBlock(block.getRelative(x, y + 1, z), player, Material.BEETROOTS,
                                                null)) {
                                            Utilities.removeItem(player, Material.BEETROOT_SEEDS, 1);
                                        }
                                    }
                                } else if (block.getRelative(x, y, z).getType() == SOUL_SAND
                                        && Storage.COMPATIBILITY_ADAPTER.airs().contains(block.getRelative(x, y + 1, z).getType())) {
                                    if (evt.getPlayer().getInventory().contains(Material.NETHER_WART)) {
                                        if (ADAPTER.placeBlock(block.getRelative(x, y + 1, z), player, Material.NETHER_WART,
                                                null)) {
                                            Utilities.removeItem(player, Material.NETHER_WART, 1);
                                        }
                                    }
                                } else {
                                    continue;
                                }
                                if (ThreadLocalRandom.current().nextBoolean()) {
                                    CompatibilityAdapter.damageTool(evt.getPlayer(), 1, usedHand);
                                }
                            }
                        }
                    }
                }
                return true;
            }
        }
        return false;
    }

    /**
     * The Area of effect multiplier used by this enchantment.
     * @since 2.1.6
     * @see AreaOfEffectable
     */
    private double aoe = 1.0;
    
    @Override
    public double getAOESize(int level) {
        return 3 + aoe + level * 2;
    }

    @Override
    public double getAOEMultiplier() {
        return aoe;
    }

    /**
     * Sets the multiplier used for the area of effect size calculation, the multiplier should have in most cases a linear impact,
     * however it's not guaranteed that the AOE Size is linear to the multiplier as some other effects may play a role.<br>
     * <br>
     * Impact formula: <b>3 + AOE + (2 * level)</b>
     * @param newValue The new value of the multiplier
     * @since 2.1.6
     */
    @Override
    public void setAOEMultiplier(double newValue) {
        aoe = newValue;
    }

}

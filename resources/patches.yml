piston:
  enable: true
explosion:
  enable: true
  removeBlocksInsteadOfCancel: false
patch_ench_protect:
  netherstep_removeBlocksInsteadOfCancel: false
  frozenstep_removeBlocksInsteadOfCancel: false
worldProtection:
  spectral: true
  native: true # If false the Plugin supports more Compatibility with world protection plugins however also allows for more exploits with plugins such as McMMO or Jobs.
  fire: false # Disabled per default for performance reasons, turning it on fixes exploits with improperly coded world protection plugins like Worldguard
  wg: false # Worldguard region integration. Flag is "eplus"
recipe:
  misc:
    arborist-doGoldenAppleDrop: true
nerfs:
  siphonsubstractAmour: true
  shredCoolDownOnLaser: true
buffs:
  # Whether the XP collected by the vortex enchantment should go towards repairing tools with the mending enchantment.
  # For the best experience, use Paper - however spigot also allows this with workarounds (which would enable others to steal the XP though).
  vortexApplyMending: true

# Specifies the reader / writer for enchantments. They can be divided into two main categories, NBT getters and Lore getters, NBT getters are more stable, however can bork other plugins and are inefficent
# Lore Getters are more efficent, however prone to break with some edge-case plugins. they also have implicit backwards-compatibillity with zenchantments
# NBT The highly configurable, but laggy NBT getter
# lwNBT The not very configurable, but stable NBT getter
# PR47-lore The not very configurable, but stable Lore getter, the name comes from the PR it was unveiled to the world first, Zedly/Zenchantments#47
# advLore The higly configurable experimental Lore getter
enchantmentGatherer: "advLore"
# the list of materials can be found at https://hub.spigotmc.org/javadocs/spigot/org/bukkit/Material.html. The plugin will only attempt to read enchantments from these items.
# Only works for advanced lore getters
getterAllow:
  - "SWORD"
  - "AXE"
  - "SHOVEL"
  - "HOE"
  - "SHEARS"
  - "HELMET"
  - "CHESTPLATE"
  - "LEGGINGS"
  - "BOOTS"
  - "FISHING_ROD"
  - "BOW"
  - "ELYTRA"
  - "FLINT_AND_STEEL"
  - "BOOK"
  - "TRIDENT"
denyPartial: true # whether even partial matches shall be denied (dangerous for some things, such as GRASS (You could also mean GRASS_PATH and GRASS_BLOCK)!), requires slightly longer startup times, but sleaker configuration.
isAllowlist: true # handles whether the above list is interpretated as an allowlist (if set to true) or used as a denylist (if set to false)
anvil-merger: "2xx" # Can either be 1xx / legacy, 2xx / new or none. Do not use "2xx"/"new" in combination with spigot.

# Whether to hide enchantments from the lore when the HIDE_ENCHANTS item flag is present.
# This requires a NBT-based/PDC-based enchantment gatherer, lore-based gatherers ignore this flag due to obvious reasons. (i. e. they cannot obtain the enchantments otherwise)
# Default is false as otherwise you would get strange behaviour when applying new enchantments on some old items, old (pre-4.0.4) behaviour is false.
# It default to true for new configs however as they are likely to run on new servers, which will not experience this issue.
# Enabling this automatically disables enchantment glow (which apparently it not working either way heh).
automaticallyHideEnchantments: yes

pluginCompat: # Of course the compatibillities will be automatically disabled if the plugin was not found
  mobstacker: true # This applies to all mobstacker plugins that have an integration coded for them. Manually prevents issues with the reveal enchantment and the shulkers it spawns
# The modes that can be used by the pierce enchantment. It is not recommended to remove the NORMAL entry as it will produce unexpected results. If pierce-modes is empty then all modes are enabled.
pierce-modes:
  - "LONG"
  - "NORMAL"
  - "TALL"
  - "VEIN"
  - "WIDE"
